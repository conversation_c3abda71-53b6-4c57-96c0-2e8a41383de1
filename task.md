
任务1：
步骤1：执行下面的命令获取到：
curl 'https://www.shixiseng.com/app/interns/search/v2?build_time=1748353047895&page=1&type=intern&keyword=%E4%BA%A7%E5%93%81&area=&months=&days=&degree=&official=&enterprise=&salary=-0&publishTime=wek&sortType=&city=%E5%85%A8%E5%9B%BD&internExtend=' \
  -H 'accept: application/json, text/plain, */*' \
  -H 'accept-language: zh-CN,zh;q=0.9' \
  -H 'cache-control: no-cache' \
  -H 'content-type: application/json' \
  -H 'pragma: no-cache'


步骤2：获取到的JSON内容需要通过Unicode转中文操作转成中文,获取到下面的JSON结构


```json
{
   "code" : 100,
   "msg" : {
      "data" : [
         {
            "ad_type" : "f",
            "c_tags" : [],
            "c_uuid" : "com_cjezc9otywqb",
            "city" : "北京",
            "cname" : "美团",
            "day" : "&#xf449",
            "degree" : "本科",
            "deliver" : false,
            "ftype" : "1",
            "hope_you" : [],
            "i_tags" : [
               "免费班车",
               "周末双休"
            ],
            "industry" : "互联网/游戏/软件",
            "invite" : false,
            "is_hr" : false,
            "is_view" : false,
            "job_label" : {
               "is_double_selection" : false,
               "is_hirer" : true,
               "is_ji" : false,
               "is_quick" : false,
               "is_rpo" : false
            },
            "maxsal" : "&#xeb46&#xf449&#xebc1",
            "maxsalary" : 250,
            "minsal" : "&#xeb46&#xebc1&#xebc1",
            "minsalary" : 200,
            "month_num" : "&#xf1c5",
            "name" : " 用户产品/策略产品（实习&#xe04f岗）",
            "refresh" : "",
            "scale" : "&#xeb46&#xebc1&#xebc1&#xebc1&#xe528以上",
            "skill" : [],
            "talkFace" : 250,
            "type" : "intern",
            "url" : "https://sxsimg.xiaoyuanzhao.com/61/80/619CBC6070EA409410303439DDF9B480.png",
            "uuid" : "inn_en0bsgprfmfz"
         }
      ],
      "pageNumber" : 20,
      "total" : 1074
   }
}

```

步骤3：获取当前数据结构中 msg中data数组中的每个对象，提取出 uuid， cname，city，degree，minsalary，maxsalary,industry信息

步骤4： 把上面的提取出来的内容写入到一个Excel文件中，修改步骤1中URL地址的page值，逐步递增，一直到返回的数据中 msg中的data数组为空时结束请求。
步骤5：最后把数据写入到一个Excel文件中。

任务2：
步骤1：遍历任务1生成的Excel文件提取出uuid 构建一个https://wap.shixiseng.com/intern/{uuid}的 网页请求 比如上面的例子就构建出 https://wap.shixiseng.com/intern/inn_en0bsgprfmfz 的URL地址。
步骤2：获取这个URL地址的html数据，注意需要把 user-agent 设置成手机浏览器，才能获取到html文件。
```shell
curl 'https://wap.shixiseng.com/intern/inn_en0bsgprfmfz' \
  -H 'accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7' \
  -H 'accept-language: zh-CN,zh;q=0.9' \
  -H 'cache-control: no-cache' \
  -H 'user-agent: Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1'
```
步骤3：获取到URL地址html 获取到 body中 <div class="content"> 中间的内容,如 demo.html文件所示。
步骤4：获取html中的数据，提取出下面内容，注意html结构类似，值会因为uuid不同而不同。

"<p class="intern_name"> 用户产品/策略产品（实习生岗）</p> "  获取 intern_name 值为 "用户产品/策略产品（实习生岗）"
"<p class="salary">200-250/天</p>"  获取 "salary" 值为 "200-250/天"
"<div class="desc-content" style="white-space:pre-wrap;">"  之间的内容 作为 "desc-content"的值
"<div class="require"><p>简历要求：中文</p><p>截止时间：2025-06-29</p> </div>" 获取 "deadline" 值为 "2025-06-29"
"<div class="address">北京市/北京市/朝阳区 望京国际研发园</div>" 获取 address 值 为 "北京市/北京市/朝阳区 望京国际研发园" 
获取"<div class="intern_tags">"中每个 "<span class="tag_name">"的值 ，值之间使用"/"进行连接起来，把连接后的值设置到 intern_tags中

步骤5：更新Excel文件把 intern_name，salary，desc-content，deadline，address，intern_tags更新到对应的Excel文件中对应的uuid中对应的列。
